%% 4自由度R-P-P-R机器人 - 斜直线运动（正逆解+动画）
clear; clc; close all;

%% 机器人参数
robot.L_blue = 1.0;         % 蓝色臂长度
robot.W_blue = 0.06;        % 蓝色臂宽度
robot.L_yellow = 0.15;      % 黄色滑块长度
robot.W_yellow = 0.14;      % 黄色滑块宽度
robot.H_yellow = 0.05;      % 黄色滑块高度
robot.L_green = 0.6;        % 绿色臂长度
robot.W_green = 0.04;       % 绿色臂宽度
robot.L_purple = 0.1;       % 紫色执行器长度
robot.W_purple = 0.07;      % 紫色执行器宽度
robot.H_purple = 0.07;      % 紫色执行器高度

% D-H参数表
robot.dh_params = [
    0,       0,                                     robot.L_blue,   0;        % 关节1: 蓝色臂绕X轴旋转
    0,       robot.W_blue/2 + robot.H_yellow/2,    0,              pi/2;     % 关节2: 黄色滑块沿X轴平移
    0,       0,                                     0,              -pi/2;    % 关节3: 绿色臂沿Y轴平移
    0,       robot.H_yellow/2 + robot.W_green/2,   robot.L_green + robot.L_purple,  pi/2;     % 关节4: 紫色机械臂绕X轴旋转，到达末端
];

% 关节类型定义 (1=旋转, 0=平移)
robot.joint_types = [1, 0, 0, 1];  % R-P-P-R

% 关节限制
green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
robot.q_limits = [
    -deg2rad(60),  deg2rad(60);     % q1: 蓝色臂绕X轴旋转 ±60°
    0,             0.46;            % q2: 黄色滑块沿X轴平移 0-0.46m
    -green_slide_limit, green_slide_limit;  % q3: 绿色臂Y轴平移
    -pi,           pi               % q4: 紫色执行器绕X轴旋转 ±180°
];

fprintf('=== 4自由度R-P-P-R机器人 - 紫色机械臂保持水平 ===\n');

%% 图形设置
figure('Name', 'R-P-P-R Robot - Diagonal Line Motion', 'NumberTitle', 'off', 'Color', 'w', 'Position', [100, 100, 1200, 800]);
hold on; axis equal; grid on;
view(135, 25);
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('4-DOF R-P-P-R Robot - Original Motion with Horizontal Purple Arm');
xlim([-0.2, 1.8]); ylim([-0.8, 0.8]); zlim([-1.2, 0.4]);  % 扩大Z轴范围，向下移动
camlight; lighting gouraud;

%% 视频设置
video_filename = 'rppr_robot_horizontal_purple.mp4';
video_writer = VideoWriter(video_filename, 'MPEG-4');
video_writer.FrameRate = 20;
video_writer.Quality = 95;
open(video_writer);

%% 创建图形对象
unit_cube_vertices = [-0.5 -0.5 -0.5; 0.5 -0.5 -0.5; 0.5 0.5 -0.5; -0.5 0.5 -0.5;
                      -0.5 -0.5 0.5; 0.5 -0.5 0.5; 0.5 0.5 0.5; -0.5 0.5 0.5]';
unit_cube_faces = [1 2 6 5; 2 3 7 6; 3 4 8 7; 4 1 5 8; 1 2 3 4; 5 6 7 8];
initial_vertices = zeros(8, 3);

h_blue_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'b', 'EdgeColor', 'k');
h_yellow_slider = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'y', 'EdgeColor', 'k');
h_green_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'g', 'EdgeColor', 'k');
h_purple_effector = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', [0.6, 0.2, 0.8], 'EdgeColor', 'k');

% 参数显示文本框
h_text = annotation('textbox', [0.78, 0.75, 0.2, 0.2], 'String', 'Ready', 'FontSize', 10, ...
                   'VerticalAlignment', 'top', 'EdgeColor', 'k', 'BackgroundColor', 'w', 'FitBoxToText', 'on');

%% 机器人整体下移偏移量
robot_offset_z = -0.6;  % 增加下移距离，从-0.3改为-0.6

%% 动画仿真
fprintf('开始机器人运动动画仿真和视频录制（紫色机械臂保持水平）...\n');

dt = 0.05;
animation_duration = 10;
frame_count = 0;

for t = 0:dt:animation_duration
    % 保持原有的关节轨迹规划（除了q4）
    q1 = deg2rad(60) * sin(t * pi/8);                    % 蓝色臂旋转
    q2 = 0.46 * (0.5 * (1-cos(t*pi/6)));                 % 黄色滑块平移
    q3 = green_slide_limit * sin(t*pi/5);                % 绿色臂平移

    % q4: 紫色机械臂绕X轴旋转角度（保持水平，补偿蓝色臂旋转）
    q4 = -q1;  % 反向补偿蓝色臂的旋转，使紫色机械臂始终保持水平

    % 应用关节限制
    q = apply_joint_limits([q1, q2, q3, q4], robot);

    % D-H正运动学计算末端位置
    [~, T_end] = forward_kinematics_dh(q, robot);
    end_pos = T_end(1:3, 4);
    end_pos(3) = end_pos(3) + robot_offset_z;  % 应用Z轴偏移

    % 绘制机器人
    draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, robot_offset_z);

    % 更新参数显示
    param_str = sprintf('机器人运动 (紫色机械臂保持水平):\nq1(绕X轴): %+.1f°\nq2(沿X轴): %.3fm\nq3(沿Y轴): %+.3fm\nq4(绕X轴): %.1f°\n\n末端位置:\nX: %.3f\nY: %.3f\nZ: %.3f\n\n时间: %.1fs',...
                       rad2deg(q(1)), q(2), q(3), rad2deg(q(4)), ...
                       end_pos(1), end_pos(2), end_pos(3), t);
    set(h_text, 'String', param_str);

    drawnow;

    % 录制视频帧
    frame = getframe(gcf);
    writeVideo(video_writer, frame);
    frame_count = frame_count + 1;

    pause(dt);
end

% 关闭视频文件
close(video_writer);

fprintf('机器人运动动画完成！\n');
fprintf('视频已保存为: %s\n', video_filename);
fprintf('总帧数: %d\n', frame_count);

%% 逆运动学验证示例
fprintf('\n=== 逆运动学验证示例 ===\n');
% 测试目标位置（选择机器人工作空间内的合理位置）
test_target = [1.3, 0.3, -0.9];  % 调整到更合理的位置
fprintf('目标位置: [%.3f, %.3f, %.3f]\n', test_target(1), test_target(2), test_target(3));

% 使用解析逆运动学（保持紫色机械臂水平）
q_analytical = inverse_kinematics_horizontal(test_target, robot);
fprintf('解析逆解: q1=%.1f°, q2=%.3fm, q3=%.3fm, q4=%.1f°\n', ...
        rad2deg(q_analytical(1)), q_analytical(2), q_analytical(3), rad2deg(q_analytical(4)));

% 验证正运动学
[~, T_verify] = forward_kinematics_dh(q_analytical, robot);
actual_pos = T_verify(1:3, 4);
fprintf('实际位置: [%.3f, %.3f, %.3f]\n', actual_pos(1), actual_pos(2), actual_pos(3));
fprintf('位置误差: %.6f m\n', norm(test_target - actual_pos'));

% 使用数值逆运动学（通用版本）
q_numerical = inverse_kinematics_numerical(test_target, robot);
fprintf('数值逆解: q1=%.1f°, q2=%.3fm, q3=%.3fm, q4=%.1f°\n', ...
        rad2deg(q_numerical(1)), q_numerical(2), q_numerical(3), rad2deg(q_numerical(4)));

% 验证数值解
[~, T_verify_num] = forward_kinematics_dh(q_numerical, robot);
actual_pos_num = T_verify_num(1:3, 4);
fprintf('数值解实际位置: [%.3f, %.3f, %.3f]\n', actual_pos_num(1), actual_pos_num(2), actual_pos_num(3));
fprintf('数值解位置误差: %.6f m\n', norm(test_target - actual_pos_num'));

%% 逆运动学函数（保持紫色机械臂水平）
function q = inverse_kinematics_horizontal(target_pos, robot)
    % 逆运动学求解，确保紫色机械臂始终与X轴保持水平

    % 从目标位置反推各关节角度
    x_target = target_pos(1);
    y_target = target_pos(2);
    z_target = target_pos(3);

    % 考虑紫色机械臂长度，计算绿色臂末端位置（紫色机械臂水平时）
    green_end_x = x_target - robot.L_purple;
    green_end_y = y_target;
    green_end_z = z_target;

    % q1: 蓝色臂绕X轴旋转角度
    % 根据绿色臂末端在YZ平面的位置计算
    if abs(green_end_y) < 1e-6 && abs(green_end_z) < 1e-6
        q1 = 0;  % 避免除零
    else
        q1 = atan2(green_end_z, green_end_y);
    end

    % q2: 黄色滑块沿X轴平移距离
    q2 = green_end_x - robot.L_blue;

    % q3: 绿色臂沿Y轴平移距离
    % 考虑蓝色臂旋转后，绿色臂在旋转坐标系中的位置
    y_projected = sqrt(green_end_y^2 + green_end_z^2);
    q3 = y_projected - robot.L_green;

    % q4: 紫色机械臂绕X轴旋转角度（保持水平）
    % 补偿蓝色臂的旋转，使紫色机械臂保持与全局X轴平行
    q4 = -q1;

    % 应用关节限制
    q = apply_joint_limits([q1, q2, q3, q4], robot);

    % 检查解的有效性
    if q2 < robot.q_limits(2,1) || q2 > robot.q_limits(2,2)
        warning('目标位置超出X轴工作范围');
    end
    if abs(q3) > robot.q_limits(3,2)
        warning('目标位置超出Y轴工作范围');
    end
end

%% 数值逆运动学函数（通用版本）
function q_solution = inverse_kinematics_numerical(target_pos, robot)
    % 使用数值优化方法求解逆运动学

    % 初始猜测（关节限制中点）
    q0 = zeros(4, 1);
    for i = 1:4
        q0(i) = (robot.q_limits(i,1) + robot.q_limits(i,2)) / 2;
    end

    % 优化选项
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 1000, 'TolFun', 1e-8, 'TolX', 1e-8);

    % 目标函数：最小化位置误差
    objective = @(q) calc_position_error(q, target_pos, robot);

    % 求解
    [q_solution, ~] = fmincon(objective, q0, [], [], [], [], ...
                             robot.q_limits(:,1), robot.q_limits(:,2), [], options);
end

%% 位置误差计算函数
function error = calc_position_error(q, target_pos, robot)
    % 计算位置误差
    [~, T_current] = forward_kinematics_dh(q, robot);
    current_pos = T_current(1:3, 4);
    error = norm(target_pos - current_pos);
end

%% 工作空间检查函数
function [is_reachable, workspace_info] = check_workspace(target_pos, robot)
    % 检查目标位置是否在机器人工作空间内

    x_target = target_pos(1);
    y_target = target_pos(2);
    z_target = target_pos(3);

    % 计算机器人的理论工作空间
    max_reach_x = robot.L_blue + robot.q_limits(2,2) + robot.L_green + robot.L_purple;
    min_reach_x = robot.L_blue + robot.q_limits(2,1);

    max_reach_y = robot.L_green + robot.q_limits(3,2);
    min_reach_y = -(robot.L_green + abs(robot.q_limits(3,1)));

    % 考虑蓝色臂旋转的Z轴范围
    max_reach_z = max_reach_y * sin(robot.q_limits(1,2));
    min_reach_z = max_reach_y * sin(robot.q_limits(1,1));

    % 检查是否在工作空间内
    x_ok = (x_target >= min_reach_x) && (x_target <= max_reach_x);
    y_ok = (abs(y_target) <= max_reach_y);
    z_ok = (z_target >= min_reach_z) && (z_target <= max_reach_z);

    is_reachable = x_ok && y_ok && z_ok;

    workspace_info = struct();
    workspace_info.x_range = [min_reach_x, max_reach_x];
    workspace_info.y_range = [min_reach_y, max_reach_y];
    workspace_info.z_range = [min_reach_z, max_reach_z];
    workspace_info.x_ok = x_ok;
    workspace_info.y_ok = y_ok;
    workspace_info.z_ok = z_ok;
end

%% D-H正运动学函数
function [T_matrices, T_end] = forward_kinematics_dh(q, robot)
    dh_params = robot.dh_params;
    joint_types = robot.joint_types;
    n_joints = length(q);

    T_matrices = cell(n_joints, 1);
    T_cumulative = eye(4);

    for i = 1:n_joints
        theta_0 = dh_params(i, 1);
        d_0 = dh_params(i, 2);
        a = dh_params(i, 3);
        alpha = dh_params(i, 4);

        if joint_types(i) == 1  % 旋转关节
            theta = theta_0 + q(i);
            d = d_0;
        else  % 平移关节
            theta = theta_0;
            d = d_0 + q(i);
        end

        T_i = dh_transform(theta, d, a, alpha);
        T_cumulative = T_cumulative * T_i;
        T_matrices{i} = T_cumulative;
    end

    T_end = T_cumulative;
end

%% 机器人绘制函数（带整体偏移）
function draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, offset_z)
    % 整体偏移变换
    T_offset = transl(0, 0, offset_z);

    % 蓝色臂：绕X轴旋转
    T_blue = T_offset * rotx(q(1));
    blue_vertices = transform_part(transl(robot.L_blue/2, 0, 0), ...
                                  diag([robot.L_blue, robot.W_blue, robot.W_blue]), ...
                                  T_blue, unit_cube_vertices);
    set(h_blue_arm, 'Vertices', blue_vertices);

    % 黄色滑块：在蓝色臂上滑动
    T_yellow_base = T_blue;
    T_yellow = T_yellow_base * transl(q(2), 0, robot.W_blue/2 + robot.H_yellow/2);
    yellow_vertices = transform_part(transl(0, 0, 0), ...
                                    diag([robot.L_yellow, robot.W_yellow, robot.H_yellow]), ...
                                    T_yellow, unit_cube_vertices);
    set(h_yellow_slider, 'Vertices', yellow_vertices);

    % 绿色臂：在黄色滑块上方，沿Y轴平移
    T_green_base = T_yellow * transl(0, 0, robot.H_yellow/2 + robot.W_green/2);
    T_green = T_green_base * transl(0, q(3), 0);

    green_vertices = transform_part(transl(0, robot.L_green/2, 0), ...
                                   diag([robot.W_green, robot.L_green, robot.W_green]), ...
                                   T_green, unit_cube_vertices);
    set(h_green_arm, 'Vertices', green_vertices);

    % 紫色机械臂：安装在绿色臂末端，沿X轴方向延伸
    T_green_end = T_green * transl(0, robot.L_green, 0);
    % 绕X轴旋转
    T_purple = T_green_end * rotx(q(4));

    % 紫色机械臂沿X轴方向延伸
    purple_vertices = transform_part(transl(robot.L_purple/2, 0, 0), ...
                                    diag([robot.L_purple, robot.W_purple, robot.H_purple]), ...
                                    T_purple, unit_cube_vertices);
    set(h_purple_effector, 'Vertices', purple_vertices);
end

%% 辅助函数
function q_limited = apply_joint_limits(q, robot)
    % 应用关节限制
    q_limited = q;
    for i = 1:length(q)
        q_limited(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function T = dh_transform(theta, d, a, alpha)
    % 标准D-H变换矩阵
    ct = cos(theta); st = sin(theta);
    ca = cos(alpha); sa = sin(alpha);

    T = [ct,    -st*ca,   st*sa,    a*ct;
         st,     ct*ca,  -ct*sa,    a*st;
         0,      sa,      ca,       d;
         0,      0,       0,        1];
end

function T = rotx(theta)
    % 绕X轴旋转变换矩阵
    c = cos(theta); s = sin(theta);
    T = [1, 0,  0, 0; 0, c, -s, 0; 0, s,  c, 0; 0, 0,  0, 1];
end

function T = transl(x, y, z)
    % 平移变换矩阵
    T = eye(4);
    T(1:3, 4) = [x; y; z];
end

function V_world = transform_part(T_local_pos, S_local, T_world, V_unit)
    % 变换部件顶点
    V_model_4d = T_local_pos * [S_local*V_unit; ones(1, size(V_unit, 2))];
    V_world_4d = T_world * V_model_4d;
    V_world = V_world_4d(1:3, :)';
end
