%% 4自由度R-P-P-R机器人 - 斜直线运动（正逆解+动画）
clear; clc; close all;

%% 机器人参数
robot.L_blue = 1.0;         % 蓝色臂长度
robot.W_blue = 0.06;        % 蓝色臂宽度
robot.L_yellow = 0.15;      % 黄色滑块长度
robot.W_yellow = 0.14;      % 黄色滑块宽度
robot.H_yellow = 0.05;      % 黄色滑块高度
robot.L_green = 0.6;        % 绿色臂长度
robot.W_green = 0.04;       % 绿色臂宽度
robot.L_purple = 0.1;       % 紫色执行器长度
robot.W_purple = 0.07;      % 紫色执行器宽度
robot.H_purple = 0.07;      % 紫色执行器高度

% D-H参数表
robot.dh_params = [
    0,       0,                                     robot.L_blue,   0;        % 关节1: 蓝色臂绕X轴旋转
    0,       robot.W_blue/2 + robot.H_yellow/2,    0,              pi/2;     % 关节2: 黄色滑块沿X轴平移
    0,       0,                                     0,              -pi/2;    % 关节3: 绿色臂沿Y轴平移
    0,       robot.H_yellow/2 + robot.W_green/2,   robot.L_green + robot.L_purple,  pi/2;     % 关节4: 紫色机械臂绕X轴旋转，到达末端
];

% 关节类型定义 (1=旋转, 0=平移)
robot.joint_types = [1, 0, 0, 1];  % R-P-P-R

% 关节限制
green_slide_limit = (robot.W_yellow - robot.W_green) / 2;
robot.q_limits = [
    -deg2rad(60),  deg2rad(60);     % q1: 蓝色臂绕X轴旋转 ±60°
    0,             0.46;            % q2: 黄色滑块沿X轴平移 0-0.46m
    -green_slide_limit, green_slide_limit;  % q3: 绿色臂Y轴平移
    -pi,           pi               % q4: 紫色执行器绕X轴旋转 ±180°
];

fprintf('=== 4自由度R-P-P-R机器人 - 斜直线运动 ===\n');

%% 图形设置
figure('Name', 'R-P-P-R Robot - Diagonal Line Motion', 'NumberTitle', 'off', 'Color', 'w', 'Position', [100, 100, 1200, 800]);
hold on; axis equal; grid on;
view(135, 25);
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('4-DOF R-P-P-R Robot - Diagonal Line Motion (Purple End-Effector Horizontal)');
xlim([-0.2, 1.8]); ylim([-0.8, 0.8]); zlim([-0.8, 0.2]);
camlight; lighting gouraud;

%% 视频设置
video_filename = 'rppr_robot_diagonal_motion.mp4';
video_writer = VideoWriter(video_filename, 'MPEG-4');
video_writer.FrameRate = 20;
video_writer.Quality = 95;
open(video_writer);

%% 创建图形对象
unit_cube_vertices = [-0.5 -0.5 -0.5; 0.5 -0.5 -0.5; 0.5 0.5 -0.5; -0.5 0.5 -0.5;
                      -0.5 -0.5 0.5; 0.5 -0.5 0.5; 0.5 0.5 0.5; -0.5 0.5 0.5]';
unit_cube_faces = [1 2 6 5; 2 3 7 6; 3 4 8 7; 4 1 5 8; 1 2 3 4; 5 6 7 8];
initial_vertices = zeros(8, 3);

h_blue_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'b', 'EdgeColor', 'k');
h_yellow_slider = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'y', 'EdgeColor', 'k');
h_green_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'g', 'EdgeColor', 'k');
h_purple_effector = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', [0.6, 0.2, 0.8], 'EdgeColor', 'k');

% 参数显示文本框
h_text = annotation('textbox', [0.78, 0.75, 0.2, 0.2], 'String', 'Ready', 'FontSize', 10, ...
                   'VerticalAlignment', 'top', 'EdgeColor', 'k', 'BackgroundColor', 'w', 'FitBoxToText', 'on');

%% 斜直线轨迹规划
% 定义起点和终点
start_pos = [1.2, -0.3, -0.4];  % 起点
end_pos = [1.6, 0.3, -0.1];     % 终点

% 机器人整体下移偏移量
robot_offset_z = -0.3;

%% 动画仿真
fprintf('开始斜直线运动动画仿真和视频录制...\n');

dt = 0.05;
animation_duration = 8;
frame_count = 0;

for t = 0:dt:animation_duration
    % 斜直线插值
    s = t / animation_duration;  % 归一化时间 [0,1]
    target_pos = start_pos + s * (end_pos - start_pos);
    
    % 逆运动学求解（紫色机械臂保持水平）
    q = inverse_kinematics_horizontal(target_pos, robot);
    
    % 绘制机器人
    draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, robot_offset_z);
    
    % 更新参数显示
    param_str = sprintf('斜直线运动 (紫色机械臂水平):\nq1(绕X轴): %+.1f°\nq2(沿X轴): %.3fm\nq3(沿Y轴): %+.3fm\nq4(绕X轴): %.1f°\n\n目标位置:\nX: %.3f\nY: %.3f\nZ: %.3f\n\n进度: %.1f%%',...
                       rad2deg(q(1)), q(2), q(3), rad2deg(q(4)), ...
                       target_pos(1), target_pos(2), target_pos(3), s*100);
    set(h_text, 'String', param_str);
    
    drawnow;
    
    % 录制视频帧
    frame = getframe(gcf);
    writeVideo(video_writer, frame);
    frame_count = frame_count + 1;
    
    pause(dt);
end

% 关闭视频文件
close(video_writer);

fprintf('斜直线运动动画完成！\n');
fprintf('视频已保存为: %s\n', video_filename);
fprintf('总帧数: %d\n', frame_count);

%% 逆运动学函数（保持紫色机械臂水平）
function q = inverse_kinematics_horizontal(target_pos, robot)
    % 逆运动学求解，确保紫色机械臂始终与X轴保持水平

    % 从目标位置反推各关节角度
    x_target = target_pos(1);
    y_target = target_pos(2);
    z_target = target_pos(3);

    % 考虑紫色机械臂长度，计算绿色臂末端位置
    green_end_x = x_target - robot.L_purple;
    green_end_y = y_target;
    green_end_z = z_target;

    % q1: 蓝色臂绕X轴旋转角度
    % 从绿色臂末端的Z坐标计算
    q1 = atan2(green_end_z, green_end_y);

    % q2: 黄色滑块沿X轴平移距离
    % 从绿色臂末端的X坐标计算
    q2 = green_end_x - robot.L_blue;

    % q3: 绿色臂沿Y轴平移距离
    % 考虑蓝色臂旋转后的几何关系
    y_projected = sqrt(green_end_y^2 + green_end_z^2);
    q3 = y_projected - robot.L_green;

    % q4: 紫色机械臂绕X轴旋转角度（保持水平，即与X轴平行）
    % 为了保持水平，需要补偿蓝色臂的旋转
    q4 = -q1;  % 反向补偿蓝色臂的旋转

    % 应用关节限制
    q = apply_joint_limits([q1, q2, q3, q4], robot);
end

%% D-H正运动学函数
function [T_matrices, T_end] = forward_kinematics_dh(q, robot)
    dh_params = robot.dh_params;
    joint_types = robot.joint_types;
    n_joints = length(q);

    T_matrices = cell(n_joints, 1);
    T_cumulative = eye(4);

    for i = 1:n_joints
        theta_0 = dh_params(i, 1);
        d_0 = dh_params(i, 2);
        a = dh_params(i, 3);
        alpha = dh_params(i, 4);

        if joint_types(i) == 1  % 旋转关节
            theta = theta_0 + q(i);
            d = d_0;
        else  % 平移关节
            theta = theta_0;
            d = d_0 + q(i);
        end

        T_i = dh_transform(theta, d, a, alpha);
        T_cumulative = T_cumulative * T_i;
        T_matrices{i} = T_cumulative;
    end

    T_end = T_cumulative;
end

%% 机器人绘制函数（带整体偏移）
function draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, offset_z)
    % 整体偏移变换
    T_offset = transl(0, 0, offset_z);

    % 蓝色臂：绕X轴旋转
    T_blue = T_offset * rotx(q(1));
    blue_vertices = transform_part(transl(robot.L_blue/2, 0, 0), ...
                                  diag([robot.L_blue, robot.W_blue, robot.W_blue]), ...
                                  T_blue, unit_cube_vertices);
    set(h_blue_arm, 'Vertices', blue_vertices);

    % 黄色滑块：在蓝色臂上滑动
    T_yellow_base = T_blue;
    T_yellow = T_yellow_base * transl(q(2), 0, robot.W_blue/2 + robot.H_yellow/2);
    yellow_vertices = transform_part(transl(0, 0, 0), ...
                                    diag([robot.L_yellow, robot.W_yellow, robot.H_yellow]), ...
                                    T_yellow, unit_cube_vertices);
    set(h_yellow_slider, 'Vertices', yellow_vertices);

    % 绿色臂：在黄色滑块上方，沿Y轴平移
    T_green_base = T_yellow * transl(0, 0, robot.H_yellow/2 + robot.W_green/2);
    T_green = T_green_base * transl(0, q(3), 0);

    green_vertices = transform_part(transl(0, robot.L_green/2, 0), ...
                                   diag([robot.W_green, robot.L_green, robot.W_green]), ...
                                   T_green, unit_cube_vertices);
    set(h_green_arm, 'Vertices', green_vertices);

    % 紫色机械臂：安装在绿色臂末端，沿X轴方向延伸
    T_green_end = T_green * transl(0, robot.L_green, 0);
    % 绕X轴旋转
    T_purple = T_green_end * rotx(q(4));

    % 紫色机械臂沿X轴方向延伸
    purple_vertices = transform_part(transl(robot.L_purple/2, 0, 0), ...
                                    diag([robot.L_purple, robot.W_purple, robot.H_purple]), ...
                                    T_purple, unit_cube_vertices);
    set(h_purple_effector, 'Vertices', purple_vertices);
end

%% 辅助函数
function q_limited = apply_joint_limits(q, robot)
    % 应用关节限制
    q_limited = q;
    for i = 1:length(q)
        q_limited(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function T = dh_transform(theta, d, a, alpha)
    % 标准D-H变换矩阵
    ct = cos(theta); st = sin(theta);
    ca = cos(alpha); sa = sin(alpha);

    T = [ct,    -st*ca,   st*sa,    a*ct;
         st,     ct*ca,  -ct*sa,    a*st;
         0,      sa,      ca,       d;
         0,      0,       0,        1];
end

function T = rotx(theta)
    % 绕X轴旋转变换矩阵
    c = cos(theta); s = sin(theta);
    T = [1, 0,  0, 0; 0, c, -s, 0; 0, s,  c, 0; 0, 0,  0, 1];
end

function T = transl(x, y, z)
    % 平移变换矩阵
    T = eye(4);
    T(1:3, 4) = [x; y; z];
end

function V_world = transform_part(T_local_pos, S_local, T_world, V_unit)
    % 变换部件顶点
    V_model_4d = T_local_pos * [S_local*V_unit; ones(1, size(V_unit, 2))];
    V_world_4d = T_world * V_model_4d;
    V_world = V_world_4d(1:3, :)';
end
